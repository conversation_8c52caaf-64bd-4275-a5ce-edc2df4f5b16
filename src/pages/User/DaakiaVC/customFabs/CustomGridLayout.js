import { useRef, useMemo } from 'react';
import {
  useGridLayout,
  usePagination,
  useSwipe,
  TrackLoop
} from '@livekit/components-react';

// Simple mergeProps utility function
function mergeProps(...propObjects) {
  const merged = {};

  propObjects.forEach(props => {
    if (props) {
      Object.keys(props).forEach(key => {
        if (key === 'className') {
          merged.className = merged.className
            ? `${merged.className} ${props.className}`
            : props.className;
        } else {
          merged[key] = props[key];
        }
      });
    }
  });

  return merged;
}

// Simple pagination indicator component
function PaginationIndicator({ totalPageCount, currentPage }) {
  if (totalPageCount <= 1) return null;

  return (
    <div className="lk-pagination-indicator">
      <div className="pagination-info">
        Page {currentPage + 1} of {totalPageCount}
      </div>
      {Array.from({ length: totalPageCount }, (_, i) => (
        <div
          key={i}
          className={`pagination-dot ${i === currentPage ? 'active' : ''}`}
        />
      ))}
    </div>
  );
}

// Simple pagination control component
function PaginationControl({ nextPage, prevPage, currentPage, totalPageCount, setPage }) {
  if (totalPageCount <= 1) return null;

  const handlePrevClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Prev clicked:', { currentPage, totalPageCount, prevPage: typeof prevPage });
    if (currentPage > 0 && prevPage) {
      console.log('Calling prevPage()');
      prevPage();
    }
  };

  const handleNextClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Next clicked:', { currentPage, totalPageCount, nextPage: typeof nextPage });
    if (currentPage < totalPageCount - 1 && nextPage) {
      console.log('Calling nextPage()');
      nextPage();
    }
  };

  const handlePageClick = (pageIndex) => {
    console.log('Direct page click:', pageIndex);
    if (setPage) {
      setPage(pageIndex);
    }
  };

  return (
    <div className="lk-pagination-control">
      <button
        onClick={handlePrevClick}
        disabled={currentPage === 0}
        className="pagination-btn"
        type="button"
      >
        ‹
      </button>

      {/* Page number buttons for direct navigation */}
      {Array.from({ length: totalPageCount }, (_, i) => (
        <button
          key={i}
          onClick={() => handlePageClick(i)}
          className={`pagination-btn page-btn ${i === currentPage ? 'active' : ''}`}
          type="button"
        >
          {i + 1}
        </button>
      ))}

      <button
        onClick={handleNextClick}
        disabled={currentPage === totalPageCount - 1}
        className="pagination-btn"
        type="button"
      >
        ›
      </button>
    </div>
  );
}

/**
 * Custom Grid Layout Component
 * - Shows 1-5 participants without pagination
 * - Shows 6+ participants with pagination (max 9 per page)
 * - Responsive design for mobile and desktop
 */
export function CustomGridLayout({ tracks, children, ...props }) {
  const gridEl = useRef(null);
  const trackCount = tracks.length;

  // Determine items per page based on track count
  const itemsPerPage = useMemo(() => {
    if (trackCount <= 5) {
      return trackCount; // Show all participants (1-5)
    } else {
      return 9; // Show 9 per page for 6+ participants (3x3 grid)
    }
  }, [trackCount]);

  // Merge props with custom className
  const elementProps = useMemo(
    () => mergeProps(props, { 
      className: 'lk-custom-grid-layout',
      'data-track-count': trackCount 
    }),
    [props, trackCount]
  );

  // Use LiveKit's grid layout hook for responsive calculations
  useGridLayout(gridEl, itemsPerPage);
  
  // Use pagination for 6+ participants
  const pagination = usePagination(itemsPerPage, tracks);

  // Add swipe support for mobile
  useSwipe(gridEl, {
    onLeftSwipe: pagination.nextPage,
    onRightSwipe: pagination.prevPage,
  });

  // Determine if pagination should be shown
  const showPagination = trackCount > itemsPerPage;

  // Debug logging
  console.log('CustomGridLayout Debug:', {
    trackCount,
    itemsPerPage,
    totalPageCount: pagination.totalPageCount,
    currentPage: pagination.currentPage,
    tracksOnCurrentPage: pagination.tracks.length,
    showPagination,
    paginationObject: pagination,
    nextPageFunction: typeof pagination.nextPage,
    prevPageFunction: typeof pagination.prevPage
  });

  return (
    <div
      ref={gridEl}
      data-lk-pagination={pagination.totalPageCount > 1}
      data-lk-track-count={pagination.tracks.length} // Use current page track count
      {...elementProps}
    >
      <TrackLoop tracks={pagination.tracks}>
        {children}
      </TrackLoop>

      {showPagination && (
        <>
          <PaginationIndicator
            totalPageCount={pagination.totalPageCount}
            currentPage={pagination.currentPage}
          />
          <PaginationControl
            nextPage={pagination.nextPage}
            prevPage={pagination.prevPage}
            currentPage={pagination.currentPage}
            totalPageCount={pagination.totalPageCount}
            setPage={pagination.setPage}
          />
        </>
      )}
    </div>
  );
}

export default CustomGridLayout;
