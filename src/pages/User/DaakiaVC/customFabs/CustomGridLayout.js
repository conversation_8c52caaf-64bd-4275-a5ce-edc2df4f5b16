import { useRef, useMemo } from 'react';
import {
  useGridLayout,
  usePagination,
  useSwipe,
  TrackLoop
} from '@livekit/components-react';

// Simple mergeProps utility function
function mergeProps(...propObjects) {
  const merged = {};

  propObjects.forEach(props => {
    if (props) {
      Object.keys(props).forEach(key => {
        if (key === 'className') {
          merged.className = merged.className
            ? `${merged.className} ${props.className}`
            : props.className;
        } else {
          merged[key] = props[key];
        }
      });
    }
  });

  return merged;
}

// Simple pagination indicator component
function PaginationIndicator({ totalPageCount, currentPage }) {
  if (totalPageCount <= 1) return null;

  return (
    <div className="lk-pagination-indicator">
      {Array.from({ length: totalPageCount }, (_, i) => (
        <div
          key={i}
          className={`pagination-dot ${i === currentPage ? 'active' : ''}`}
        />
      ))}
    </div>
  );
}

// Simple pagination control component
function PaginationControl({ nextPage, prevPage, currentPage, totalPageCount }) {
  if (totalPageCount <= 1) return null;

  return (
    <div className="lk-pagination-control">
      <button
        onClick={prevPage}
        disabled={currentPage === 0}
        className="pagination-btn"
      >
        ‹
      </button>
      <button
        onClick={nextPage}
        disabled={currentPage === totalPageCount - 1}
        className="pagination-btn"
      >
        ›
      </button>
    </div>
  );
}

/**
 * Custom Grid Layout Component
 * - Shows 1-5 participants without pagination
 * - Shows 6+ participants with pagination (max 9 per page)
 * - Responsive design for mobile and desktop
 */
export function CustomGridLayout({ tracks, children, ...props }) {
  const gridEl = useRef(null);
  const trackCount = tracks.length;

  // Determine items per page based on track count
  const itemsPerPage = useMemo(() => {
    if (trackCount <= 5) {
      return trackCount; // Show all participants (1-5)
    } else {
      return 6; // Show 6 per page for 6+ participants, max 9 total
    }
  }, [trackCount]);

  // Merge props with custom className
  const elementProps = useMemo(
    () => mergeProps(props, { 
      className: 'lk-custom-grid-layout',
      'data-track-count': trackCount 
    }),
    [props, trackCount]
  );

  // Use LiveKit's grid layout hook for responsive calculations
  useGridLayout(gridEl, itemsPerPage);
  
  // Use pagination for 6+ participants
  const pagination = usePagination(itemsPerPage, tracks);

  // Add swipe support for mobile
  useSwipe(gridEl, {
    onLeftSwipe: pagination.nextPage,
    onRightSwipe: pagination.prevPage,
  });

  // Determine if pagination should be shown
  const showPagination = trackCount > itemsPerPage;

  return (
    <div 
      ref={gridEl} 
      data-lk-pagination={pagination.totalPageCount > 1}
      data-lk-track-count={trackCount}
      {...elementProps}
    >
      <TrackLoop tracks={pagination.tracks}>
        {children}
      </TrackLoop>
      
      {showPagination && (
        <>
          <PaginationIndicator
            totalPageCount={pagination.totalPageCount}
            currentPage={pagination.currentPage}
          />
          <PaginationControl
            nextPage={pagination.nextPage}
            prevPage={pagination.prevPage}
            currentPage={pagination.currentPage}
            totalPageCount={pagination.totalPageCount}
          />
        </>
      )}
    </div>
  );
}

export default CustomGridLayout;
