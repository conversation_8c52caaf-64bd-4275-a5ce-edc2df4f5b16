// Custom Grid Layout Styles
// Responsive grid layout for video conference participants

.lk-custom-grid-layout {
  display: grid;
  gap: 8px;
  width: 100%;
  height: 100%;
  padding: 8px;
  position: relative;

  // Default grid configurations for different participant counts
  
  // 1 participant - full screen
  &[data-track-count="1"] {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }

  // 2 participants - side by side on desktop, stacked on mobile
  &[data-track-count="2"] {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr 1fr;
    }
  }

  // 3 participants - 2 top, 1 bottom
  &[data-track-count="3"] {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    
    .lk-participant-tile:nth-child(3) {
      grid-column: 1 / -1;
    }
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      grid-template-rows: repeat(3, 1fr);
      
      .lk-participant-tile:nth-child(3) {
        grid-column: 1;
      }
    }
  }

  // 4 participants - 2x2 grid
  &[data-track-count="4"] {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      grid-template-rows: repeat(4, 1fr);
    }
  }

  // 5 participants - 3 top, 2 bottom
  &[data-track-count="5"] {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 1fr 1fr;
    
    .lk-participant-tile:nth-child(4),
    .lk-participant-tile:nth-child(5) {
      grid-row: 2;
    }
    
    .lk-participant-tile:nth-child(4) {
      grid-column: 1 / 2;
    }
    
    .lk-participant-tile:nth-child(5) {
      grid-column: 2 / 3;
    }
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      grid-template-rows: repeat(5, 1fr);
      
      .lk-participant-tile:nth-child(4),
      .lk-participant-tile:nth-child(5) {
        grid-row: auto;
        grid-column: 1;
      }
    }
  }

  // 6+ participants - 3x3 grid (9 per page)
  &[data-track-count]:not([data-track-count="1"]):not([data-track-count="2"]):not([data-track-count="3"]):not([data-track-count="4"]):not([data-track-count="5"]) {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(5, 1fr);
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      grid-template-rows: repeat(9, 1fr);
    }
  }

  // Dynamic grid for different participant counts per page
  &[data-lk-pagination="true"] {
    // Adjust grid based on actual participants shown
    .lk-participant-tile {
      &:nth-child(1):nth-last-child(1) {
        // 1 participant - full grid
        grid-column: 1 / -1;
        grid-row: 1 / -1;
      }

      &:nth-child(1):nth-last-child(2),
      &:nth-child(2):nth-last-child(1) {
        // 2 participants - split grid
        grid-column: span 1;
        grid-row: 1 / -1;
      }

      &:nth-child(1):nth-last-child(3) {
        // 3 participants - first takes 2 columns
        grid-column: 1 / 3;
        grid-row: 1;
      }

      &:nth-child(2):nth-last-child(2),
      &:nth-child(3):nth-last-child(1) {
        // 3 participants - last two split bottom
        grid-column: span 1;
        grid-row: 2;
      }
    }
  }

  // Pagination controls styling
  .lk-pagination-indicator {
    position: absolute;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .pagination-info {
      color: white;
      font-size: 12px;
      background-color: rgba(0, 0, 0, 0.6);
      padding: 4px 8px;
      border-radius: 12px;
    }

    .pagination-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.5);
      transition: background-color 0.2s ease;

      &.active {
        background-color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  .lk-pagination-control {
    position: absolute;
    bottom: 16px;
    right: 16px;
    z-index: 10;
    display: flex;
    gap: 8px;

    .pagination-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid rgba(255, 255, 255, 0.3);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      user-select: none;

      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.9);
        border-color: rgba(255, 255, 255, 0.6);
        transform: scale(1.1);
      }

      &:active:not(:disabled) {
        transform: scale(0.95);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
        border-color: rgba(255, 255, 255, 0.1);
      }

      &.page-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
        border-radius: 4px;

        &.active {
          background-color: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  // Mobile specific adjustments
  @media (max-width: 768px) {
    gap: 4px;
    padding: 4px;
    
    .lk-pagination-indicator {
      bottom: 8px;
    }
    
    .lk-pagination-control {
      bottom: 8px;
      right: 8px;
    }
  }

  // Ensure participant tiles fill the grid properly
  .lk-participant-tile {
    width: 100%;
    height: 100%;
    min-height: 120px;
    border-radius: 8px;
    overflow: hidden;
    
    @media (max-width: 768px) {
      min-height: 100px;
      border-radius: 4px;
    }
  }
}

// Additional responsive breakpoints
@media (max-width: 1024px) {
  .lk-custom-grid-layout {
    gap: 6px;
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .lk-custom-grid-layout {
    gap: 2px;
    padding: 2px;
    
    .lk-participant-tile {
      min-height: 80px;
    }
  }
}
